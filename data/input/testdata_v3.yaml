test_cases:
- id: test_001
  claim: "Im Bautechnischen Dossier werden die Anforderungen aus der Langzeitsicherheit berücksichtigt, die in Kapitel 4.1 aufgeführt sind. Diese <PERSON>forderungen umfassen die Gewährleistung der Langzeitsicherheit nach Verschluss des geologischen Tiefenlagers (gTL) und basieren auf fünf Sicherheitsfunktionen:\n\nS1: Isolation der radioaktiven Abfälle von der Erdoberfläche\nS2: Vollständiger Einschluss der Radionuklide für eine gewisse Zeit\nS3: Immobilisierung, Rückhaltung und langsame Freisetzung der Radionuklide\nS4: Kompatibilität der Elemente des Mehrfachbarrierensystems und der radioaktiven Abfälle untereinander und mit anderen Materialien\nS5: Langzeitstabilität des Mehrfachbarrierensystems bezüglich geologischer und klimatischer Langzeitentwicklungen\n\nDiese Anforderungen werden im Bautechnischen Dossier berücksichtigt, um sicherzustellen, dass das geologische Tiefenlager langfristig sicher ist und keine Gefahr für die Umwelt oder die Bevölkerung darstellt."
  expected_verification: "SUPPORTS"
  mock_data:
    extraction:
      facts:
        - subject: "Bautechnisches Dossier"
          predicate: "berücksichtigt"
          object: "Anforderungen aus Langzeitsicherheit"
        - subject: "Anforderungen aus Langzeitsicherheit"
          predicate: "sind aufgeführt in"
          object: "Kapitel 4.1"
        - subject: "Anforderungen"
          predicate: "umfassen"
          object: "Gewährleistung der Langzeitsicherheit"
        - subject: "Anforderungen"
          predicate: "basieren auf"
          object: "fünf Sicherheitsfunktionen"
        - subject: "S1"
          predicate: "ist"
          object: "Isolation der radioaktiven Abfälle"
        - subject: "S2"
          predicate: "ist"
          object: "vollständiger Einschluss der Radionuklide"
        - subject: "S3"
          predicate: "ist"
          object: "Immobilisierung und Rückhaltung der Radionuklide"
        - subject: "S4"
          predicate: "ist"
          object: "Kompatibilität der Barrierensystem-Elemente"
        - subject: "S5"
          predicate: "ist"
          object: "Langzeitstabilität des Mehrfachbarrierensystems"
    retrieval:
      - fact:
          subject: "Bautechnisches Dossier"
          predicate: "berücksichtigt"
          object: "Anforderungen aus Langzeitsicherheit"
        knowledge:
          - subject:
              id: "mock_bd_001"
              name: "Bautechnisches Dossier"
              similarity: 1.0
            predicate:
              id: "mock_rel_001"
              type: "berücksichtigt"
            object:
              id: "mock_req_001"
              name: "Anforderungen aus Langzeitsicherheit"
              similarity: 1.0
            metadata: ["direct_match_found"]
      - fact:
          subject: "Anforderungen aus Langzeitsicherheit"
          predicate: "sind aufgeführt in"
          object: "Kapitel 4.1"
        knowledge:
          - subject:
              id: "mock_req_001"
              name: "Anforderungen aus Langzeitsicherheit"
              similarity: 1.0
            predicate:
              id: "mock_rel_002"
              type: "sind aufgeführt in"
            object:
              id: "mock_chap_001"
              name: "Kapitel 4.1"
              similarity: 1.0
            metadata: ["direct_match_found"]
      - fact:
          subject: "S1"
          predicate: "ist"
          object: "Isolation der radioaktiven Abfälle"
        knowledge:
          - subject:
              id: "mock_s1_001"
              name: "S1"
              similarity: 1.0
            predicate:
              id: "mock_rel_003"
              type: "ist"
            object:
              id: "mock_iso_001"
              name: "Isolation der radioaktiven Abfälle"
              similarity: 1.0
            metadata: ["direct_match_found"]
