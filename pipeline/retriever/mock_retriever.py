"""
Mock Fuzzy Retriever - Returns predefined mock data instead of querying knowledge graph

This module provides a mock implementation of the fuzzy retriever that returns
predefined retrieval results for testing purposes.
"""

import logging
from typing import Dict, List
from pipeline.types.extractor_types import Extraction
from pipeline.types.retriever_types import Retrieval, Retrieval<PERSON><PERSON>ult, FactKnowledge
from config.config_types import AppConfig
from utils.mock_types import MockTestData, MockRetrievalResult

logger = logging.getLogger(__name__)


class MockFuzzyRetriever:
    """
    Mock implementation of FuzzyRetriever that returns predefined data.
    
    This class is used for testing to provide consistent, controlled
    retrieval results without relying on knowledge graph queries.
    """

    def __init__(self, config: AppConfig, mock_data: MockTestData):
        self.config = config
        self.mock_data = mock_data
        self._mock_data_by_claim = self._build_claim_lookup()
        logger.info("Mock FuzzyRetriever initialized")

    def _build_claim_lookup(self) -> Dict[str, List[MockRetrievalResult]]:
        """Build a lookup dictionary from claims to mock retrieval data."""
        lookup = {}
        for test_case in self.mock_data.test_cases:
            if test_case.mock_data and test_case.mock_data.retrieval:
                lookup[test_case.claim] = test_case.mock_data.retrieval
        return lookup

    def retrieve(self, extraction: Extraction) -> Retrieval:
        """
        Retrieve knowledge for extracted facts using mock data.

        Args:
            extraction: Extraction result containing facts to retrieve knowledge for

        Returns:
            Retrieval object containing predefined mock knowledge
        """
        logger.info(f"Mock retrieving knowledge for {len(extraction.facts)} facts")
        
        # Look up mock data for this claim
        mock_retrieval_results = self._mock_data_by_claim.get(extraction.original_claim, [])
        
        if not mock_retrieval_results:
            logger.warning(f"No mock retrieval data found for claim, returning empty results")
            return Retrieval(
                original_claim=extraction.original_claim,
                retrievals=[]
            )

        # Convert mock retrieval results to real RetrievalResult objects
        retrieval_results = []
        
        for mock_result in mock_retrieval_results:
            # Convert mock fact to real FactTriple
            fact = mock_result.fact.to_fact_triple()
            
            # Convert mock knowledge to real FactKnowledge objects
            knowledge = []
            for mock_knowledge in mock_result.knowledge:
                knowledge.append(mock_knowledge.to_fact_knowledge())
            
            retrieval_results.append(RetrievalResult(
                fact=fact,
                knowledge=knowledge
            ))

        return Retrieval(
            original_claim=extraction.original_claim,
            retrievals=retrieval_results
        )

    def retrieve_for_fact(self, fact, connections=None):
        """
        Mock implementation of retrieve_for_fact.
        
        This method is kept for compatibility but will return empty results
        since the main retrieve method handles all mock data.
        """
        logger.info(f"Mock retrieve_for_fact called for: {fact.subject} {fact.predicate} {fact.object}")
        return []

    def retrieve_for_fact_with_connections(self, fact, connections):
        """
        Mock implementation of retrieve_for_fact_with_connections.
        
        This method is kept for compatibility but will return empty results
        since the main retrieve method handles all mock data.
        """
        logger.info(f"Mock retrieve_for_fact_with_connections called for: {fact.subject} {fact.predicate} {fact.object}")
        return []
