"""
Mock Fact Extractor - Returns predefined mock data instead of using LLM

This module provides a mock implementation of the fact extractor that returns
predefined extraction results for testing purposes.
"""

import logging
from typing import Dict, Any
from pipeline.types.extractor_types import Extraction, FactTriple
from config.config_types import AppConfig
from utils.mock_types import MockTestData, MockExtraction

logger = logging.getLogger(__name__)


class MockFactExtractor:
    """
    Mock implementation of FactExtractor that returns predefined data.
    
    This class is used for testing to provide consistent, controlled
    extraction results without relying on LLM calls.
    """

    def __init__(self, config: AppConfig, mock_data: MockTestData):
        self.config = config
        self.mock_data = mock_data
        self._mock_data_by_claim = self._build_claim_lookup()
        logger.info("Mock FactExtractor initialized")

    def _build_claim_lookup(self) -> Dict[str, MockExtraction]:
        """Build a lookup dictionary from claims to mock extraction data."""
        lookup = {}
        for test_case in self.mock_data.test_cases:
            if test_case.mock_data and test_case.mock_data.extraction:
                lookup[test_case.claim] = test_case.mock_data.extraction
        return lookup

    def extract_facts(self, claim: str) -> Extraction:
        """
        Extract atomic facts from a claim using mock data.

        Args:
            claim: Input claim text

        Returns:
            Extraction object containing predefined mock facts
        """
        logger.info(f"Mock extracting facts for claim: {claim[:100]}...")
        
        # Look up mock data for this claim
        mock_extraction = self._mock_data_by_claim.get(claim)
        
        if not mock_extraction:
            logger.warning(f"No mock data found for claim, returning empty extraction")
            return Extraction(
                original_claim=claim,
                extraction_prompt_request="[MOCK] No mock data available",
                extraction_prompt_result="[MOCK] No mock data available",
                facts=[],
                entities=[],
                connections={},
                filtering_prompt_request="",
                filtering_prompt_result=""
            )

        # Convert mock facts to real FactTriple objects
        facts = [mock_fact.to_fact_triple() for mock_fact in mock_extraction.facts]
        
        # Extract entities from facts
        entities = []
        for fact in facts:
            entities.extend(fact.get_entities())
        entities = list(set(entities))  # Remove duplicates

        # Create mock prompt responses
        extraction_prompt_request = f"[MOCK] Extract facts from: {claim[:100]}..."
        extraction_prompt_result = self._format_mock_extraction_result(facts)

        return Extraction(
            original_claim=claim,
            extraction_prompt_request=extraction_prompt_request,
            extraction_prompt_result=extraction_prompt_result,
            facts=facts,
            entities=entities,
            connections={},  # Mock extractor doesn't use connections
            filtering_prompt_request="[MOCK] No filtering needed",
            filtering_prompt_result="[MOCK] No filtering performed"
        )

    def _format_mock_extraction_result(self, facts: list[FactTriple]) -> str:
        """Format facts as if they came from LLM extraction."""
        result_lines = []
        for fact in facts:
            result_lines.append(f"({fact.subject}, {fact.predicate}, {fact.object})")
        return "\n".join(result_lines)
