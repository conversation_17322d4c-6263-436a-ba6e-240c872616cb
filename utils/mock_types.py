"""
Type definitions for mock data structure.

This module contains dataclasses for loading and working with mock data
that can be used to replace real pipeline components during testing.
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from pipeline.types.extractor_types import FactTriple
from pipeline.types.retriever_types import EntityKnowledge, RelationKnowledge, FactKnowledge


@dataclass
class MockFactTriple:
    """Mock version of FactTriple for test data."""
    subject: str
    predicate: str
    object: str
    
    def to_fact_triple(self) -> FactTriple:
        """Convert to actual FactTriple."""
        return FactTriple(
            subject=self.subject,
            predicate=self.predicate,
            object=self.object
        )


@dataclass
class MockEntityKnowledge:
    """Mock version of EntityKnowledge for test data."""
    id: str
    name: str
    similarity: float = 1.0
    
    def to_entity_knowledge(self) -> EntityKnowledge:
        """Convert to actual EntityKnowledge."""
        return EntityKnowledge(
            id=self.id,
            name=self.name,
            similarity=self.similarity
        )


@dataclass
class MockRelationKnowledge:
    """Mock version of RelationKnowledge for test data."""
    id: str
    type: str
    
    def to_relation_knowledge(self) -> RelationKnowledge:
        """Convert to actual RelationKnowledge."""
        return RelationKnowledge(
            id=self.id,
            type=self.type
        )


@dataclass
class MockFactKnowledge:
    """Mock version of FactKnowledge for test data."""
    subject: Optional[MockEntityKnowledge] = None
    predicate: Optional[MockRelationKnowledge] = None
    object: Optional[MockEntityKnowledge] = None
    metadata: List[str] = field(default_factory=list)
    
    def to_fact_knowledge(self) -> FactKnowledge:
        """Convert to actual FactKnowledge."""
        return FactKnowledge(
            subject=self.subject.to_entity_knowledge() if self.subject else None,
            predicate=self.predicate.to_relation_knowledge() if self.predicate else None,
            object=self.object.to_entity_knowledge() if self.object else None,
            metadata=self.metadata
        )


@dataclass
class MockRetrievalResult:
    """Mock version of RetrievalResult for test data."""
    fact: MockFactTriple
    knowledge: List[MockFactKnowledge] = field(default_factory=list)


@dataclass
class MockExtraction:
    """Mock extraction data for test cases."""
    facts: List[MockFactTriple] = field(default_factory=list)


@dataclass
class MockRetrieval:
    """Mock retrieval data for test cases."""
    retrieval_results: List[MockRetrievalResult] = field(default_factory=list)


@dataclass
class MockTestCase:
    """Test case with mock data."""
    id: str
    claim: str
    expected_verification: str
    mock_data: Optional['MockData'] = None


@dataclass
class MockData:
    """Container for all mock data for a test case."""
    extraction: Optional[MockExtraction] = None
    retrieval: Optional[List[MockRetrievalResult]] = None


@dataclass
class MockTestData:
    """Container for all mock test data."""
    test_cases: List[MockTestCase] = field(default_factory=list)
